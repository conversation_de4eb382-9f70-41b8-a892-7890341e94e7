package com.xiaozhi.dialogue.service;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.domain.Sentence;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.OpusProcessor;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 音频服务，负责处理音频的流式和非流式发送
 */
@Service
public class AudioService {
    private static final Logger logger = LoggerFactory.getLogger(AudioService.class);

    // 帧发送时间间隔略小于OPUS_FRAME_DURATION_MS，避免因某些调度原因，导致没能在规定时间内发送，设备出现杂音
    private static final long OPUS_FRAME_SEND_INTERVAL_MS = AudioUtils.OPUS_FRAME_DURATION_MS;

    // 预缓冲帧数量
    private static final int PRE_BUFFER_FRAMES = 3;


    @Resource
    private OpusProcessor opusProcessor;

    // 使用虚拟线程池处理定时任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(
            Runtime.getRuntime().availableProcessors(),
            Thread.ofVirtual().name("audio-scheduler-", 0).factory());

    // 存储每个会话最后一次发送帧的时间戳
    private final Map<String, AtomicLong> lastFrameSentTime = new ConcurrentHashMap<>();

    // 存储首帧发送状态
    private final Map<String, AtomicBoolean> firstFrameSent = new ConcurrentHashMap<>();

    // 存储每个会话的调度任务
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    // 存储播放开始时间（纳秒）
    private final Map<String, Long> playStartTimes = new ConcurrentHashMap<>();

    // 存储每个会话的帧持续时间（毫秒）
    private final Map<String, Long> frameDurations = new ConcurrentHashMap<>();


    /**
     * 发送停止消息
     */
    public CompletableFuture<Void> sendStop(ChatSession session) {
        var sessionId = session.getSessionId();

        // 如果在播放音乐，则不停止
        if (session.isMusicPlaying()) {
            return CompletableFuture.completedFuture(null);
        }

        // 标记播放结束并清理资源
        session.setAudioPlaying(false);
        cleanupSessionResources(sessionId);

        try {
            var sendTtsMessageFuture = CompletableFuture.runAsync(session::sendTTSStop);
            session.setAudioPlaying(false);
            return sendTtsMessageFuture;
        } catch (Exception e) {
            logger.error("发送停止消息失败", e);
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }
    }


    /**
     * 发送音频消息
     *
     * @param session  WebSocketSession会话
     * @param sentence 句子对象
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendAudioMessage(ChatSession session, Sentence sentence) {
        String sessionId = session.getSessionId();
        boolean isFirst = sentence.isFirst();
        boolean isLast = sentence.isLast();

        // 初始化播放状态
        session.setAudioPlaying(true);
        session.setLastActivityTime(Instant.now());

        return sendMessageHeaders(session, sentence, isFirst)
                .thenCompose(v -> processAudioContent(session, sentence))
                .whenComplete((result, error) -> finalizePlayback(session, sessionId))
                .thenCompose(v -> sendStopIfNeeded(session, isLast))
                .exceptionally(error -> handleSendError(session, isLast, error));
    }

    /**
     * 发送消息头部信息（TTS开始、句子开始、表情）
     */
    private CompletableFuture<Void> sendMessageHeaders(ChatSession session, Sentence sentence, boolean isFirst) {
        CompletableFuture<Void> startFuture = isFirst
                ? CompletableFuture.runAsync(session::sendTTSStart)
                : CompletableFuture.completedFuture(null);

        return startFuture
                .thenRun(() -> session.sendSentenceStart(sentence.getText()))
                .thenRun(() -> sendSentenceEmotion(session, sentence, null));
    }

    /**
     * 处理音频内容
     */
    private CompletableFuture<Void> processAudioContent(ChatSession session, Sentence sentence) {
        String audioPath = sentence.getAudioPath();
        if (audioPath == null) {
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }

        return loadAudioFrames(session.getSessionId(), audioPath)
                .thenCompose(frames -> transmitAudioFrames(session, frames));
    }

    /**
     * 加载音频帧数据
     */
    private CompletableFuture<List<byte[]>> loadAudioFrames(String sessionId, String audioPath) {
        return CompletableFuture.supplyAsync(() -> {
            File audioFile = new File(audioPath);
            if (!audioFile.exists()) {
                logger.warn("音频文件不存在: {}", audioPath);
                return null;
            }

            try {
                if (audioPath.contains(".opus")) {
                    return opusProcessor.readOpus(audioFile);
                } else {
                    byte[] audioData = AudioUtils.readAsPcm(audioPath);
                    return opusProcessor.pcmToOpus(sessionId, audioData, false);
                }
            } catch (Exception e) {
                logger.error("处理音频文件失败: {}", audioPath, e);
                return null;
            }
        });
    }

    /**
     * 传输音频帧数据
     */
    private CompletableFuture<Void> transmitAudioFrames(ChatSession session, List<byte[]> opusFrames) {
        if (opusFrames == null || opusFrames.isEmpty()) {
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }

        String sessionId = session.getSessionId();
        session.setAudioPlaying(true);

        CompletableFuture<Void> transmissionFuture = new CompletableFuture<>();

        // 检测实际的帧持续时间
        long actualFrameDurationMs = detectFrameDuration(sessionId, opusFrames);
        logger.debug("检测到音频帧持续时间: {}ms", actualFrameDurationMs);

        playStartTimes.put(sessionId, System.nanoTime());
        frameDurations.put(sessionId, actualFrameDurationMs);

        try {
            sendPreBufferedFrames(session, opusFrames);
            scheduleRemainingFrames(session, opusFrames, transmissionFuture);
        } catch (Exception e) {
            logger.error("音频帧传输初始化失败", e);
            endTask(sessionId, transmissionFuture, e);
        }

        return transmissionFuture;
    }

    /**
     * 发送预缓冲帧
     */
    private void sendPreBufferedFrames(ChatSession session, List<byte[]> opusFrames) throws IOException {
        int preBufferCount = Math.min(PRE_BUFFER_FRAMES, opusFrames.size());
        for (int i = 0; i < preBufferCount; i++) {
            sendOpusFrame(session, opusFrames.get(i));
        }
    }

    /**
     * 调度剩余帧的发送
     */
    private void scheduleRemainingFrames(ChatSession session, List<byte[]> opusFrames, CompletableFuture<Void> future) {
        int startFrameIndex = Math.min(PRE_BUFFER_FRAMES, opusFrames.size());

        if (startFrameIndex >= opusFrames.size()) {
            endTask(session.getSessionId(), future);
            return;
        }

        scheduleFrameTransmission(session, opusFrames, startFrameIndex, future);
    }

    /**
     * 调度单个帧的传输
     */
    private void scheduleFrameTransmission(ChatSession session, List<byte[]> opusFrames,
                                           int frameIndex, CompletableFuture<Void> future) {
        var sessionId = session.getSessionId();

        Runnable frameTask = () -> {
            try {
                if (!session.isAudioPlaying() || frameIndex >= opusFrames.size() || !session.isOpen()) {
                    endTask(sessionId, future);
                    return;
                }

                sendOpusFrame(session, opusFrames.get(frameIndex));

                var nextFrameIndex = frameIndex + 1;
                if (nextFrameIndex < opusFrames.size()) {
                    scheduleFrameTransmission(session, opusFrames, nextFrameIndex, future);
                } else {
                    endTask(sessionId, future);
                }
            } catch (Exception e) {
                logger.error("帧发送失败", e);
                endTask(sessionId, future, e);
            }
        };

        scheduleNextFrame(sessionId, frameIndex, frameTask);
    }


    /**
     * 完成播放处理
     */
    private void finalizePlayback(ChatSession session, String sessionId) {
        session.setAudioPlaying(false);
        session.setLastActivityTime(Instant.now());
        cleanupSessionResources(sessionId);
    }

    /**
     * 根据需要发送停止消息
     */
    private CompletableFuture<Void> sendStopIfNeeded(ChatSession session, boolean isLast) {
        return isLast ? sendStop(session) : CompletableFuture.completedFuture(null);
    }

    /**
     * 处理发送错误
     */
    private Void handleSendError(ChatSession session, boolean isLast, Throwable error) {
        logger.error("发送音频消息失败", error);

        if (isLast) {
            try {
                sendStop(session);
            } catch (Exception e) {
                logger.error("发送停止消息失败", e);
            }
        }
        return null;
    }

    /**
     * 发送Opus帧数据
     */
    public void sendOpusFrame(ChatSession session, byte[] opusFrame) throws IOException {
        session.sendBinaryMessage(opusFrame);
    }

    /**
     * 发送表情信息。如果句子里没有分析出表情，则默认返回 happy
     */
    private void sendSentenceEmotion(ChatSession session, Sentence sentence, String defaultEmotion) {
        List<String> moods = sentence.getMoods();
        if (moods != null && !moods.isEmpty()) {
            session.sendEmotion(moods.get(0));
        } else if (defaultEmotion != null) {
            session.sendEmotion(defaultEmotion);
        }
    }

    /**
     * 清理会话资源（公共接口）
     */
    public void cleanupSession(String sessionId) {
        cleanupSessionResources(sessionId);
        opusProcessor.cleanup(sessionId);
    }

    /**
     * 清理会话的内部资源
     */
    private void cleanupSessionResources(String sessionId) {
        cancelScheduledTask(sessionId);
        lastFrameSentTime.remove(sessionId);
        playStartTimes.remove(sessionId);
    }

    /**
     * 结束非流式任务
     */
    private void endTask(String sessionId, CompletableFuture<Void> future) {
        endTask(sessionId, future, null);
    }

    /**
     * 结束非流式任务（带异常）
     */
    private void endTask(String sessionId, CompletableFuture<Void> future, Throwable error) {
        cleanupSessionResources(sessionId);

        // 完成Future
        if (error != null) {
            future.completeExceptionally(error);
        } else {
            future.complete(null);
        }
    }

    /**
     * 计算并调度下一帧的发送时间
     */
    private void scheduleNextFrame(String sessionId, int frameIndex, Runnable frameTask) {
        Long startTime = playStartTimes.get(sessionId);

        if (startTime == null) {
            // 如果没有开始时间，使用固定间隔
            ScheduledFuture<?> future = scheduler.schedule(frameTask, OPUS_FRAME_SEND_INTERVAL_MS, TimeUnit.MILLISECONDS);
            scheduledTasks.put(sessionId, future);
            return;
        }

        // 获取会话的帧持续时间，如果没有则使用默认值
        long frameDurationMs = frameDurations.getOrDefault(sessionId, OPUS_FRAME_SEND_INTERVAL_MS);

        // 直接用帧索引计算预期发送时间（纳秒级精度）
        long expectedTime = startTime + frameIndex * frameDurationMs * 1_000_000;
        long currentTime = System.nanoTime();
        long delayNanos = expectedTime - currentTime;

        ScheduledFuture<?> future;
        if (delayNanos <= 0) {
            // 如果当前时间已经超过预期时间，立即发送
            future = scheduler.schedule(frameTask, 0, TimeUnit.NANOSECONDS);
        } else {
            // 延迟到精确时间点再发送
            future = scheduler.schedule(frameTask, delayNanos, TimeUnit.NANOSECONDS);
        }

        scheduledTasks.put(sessionId, future);
    }


    /**
     * 取消调度任务
     */
    public void cancelScheduledTask(String sessionId) {
        ScheduledFuture<?> task = scheduledTasks.remove(sessionId);
        if (task != null && !task.isDone()) {
            task.cancel(false);
        }
    }

    /**
     * 检测音频帧的实际持续时间
     */
    private long detectFrameDuration(String sessionId, List<byte[]> opusFrames) {
        if (opusFrames.isEmpty()) {
            return OPUS_FRAME_SEND_INTERVAL_MS;
        }

        try {
            // 解码前几个帧来检测持续时间
            int totalSamples = 0;
            int frameCount = 0;
            int maxFramesToCheck = Math.min(5, opusFrames.size());

            for (int i = 0; i < maxFramesToCheck; i++) {
                try {
                    byte[] pcm = opusProcessor.opusToPcm(sessionId, opusFrames.get(i));
                    int samples = pcm.length / 2; // 16位PCM，每个样本2字节
                    totalSamples += samples;
                    frameCount++;
                } catch (Exception e) {
                    logger.debug("跳过无法解码的帧 #{}", i);
                }
            }

            if (frameCount > 0) {
                double avgSamplesPerFrame = (double) totalSamples / frameCount;
                long frameDurationMs = Math.round(avgSamplesPerFrame / 16000 * 1000); // 16kHz采样率

                // 验证帧持续时间是否合理（5ms-120ms之间）
                if (frameDurationMs >= 5 && frameDurationMs <= 120) {
                    return frameDurationMs;
                }
            }
        } catch (Exception e) {
            logger.warn("检测帧持续时间失败，使用默认值", e);
        }

        return OPUS_FRAME_SEND_INTERVAL_MS; // 使用默认值
    }

}
