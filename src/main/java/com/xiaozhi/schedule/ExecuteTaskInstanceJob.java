package com.xiaozhi.schedule;

import com.xiaozhi.common.web.OhMyLambdaQueryWrapper;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.communication.common.SessionManager;
import com.xiaozhi.communication.domain.TaskChain;
import com.xiaozhi.communication.server.mqtt.MqttServerPublish;
import com.xiaozhi.dao.ManagerMapper;
import com.xiaozhi.dao.MediaMapper;
import com.xiaozhi.dao.TaskInstanceMapper;
import com.xiaozhi.dialogue.llm.ChatService;
import com.xiaozhi.dialogue.service.DialogueService;
import com.xiaozhi.dialogue.tts.factory.TtsServiceFactory;
import com.xiaozhi.entity.Manager;
import com.xiaozhi.enums.TaskContent;
import com.xiaozhi.enums.TaskStatus;
import com.xiaozhi.utils.AudioUtils;
import com.xiaozhi.utils.OpusProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class ExecuteTaskInstanceJob {

    @Resource
    private ManagerMapper managerMapper;

    @Resource
    private TaskInstanceMapper taskInstanceMapper;

    @Resource
    private ChatService chatService;

    @Resource
    private SessionManager sessionManager;

    @Resource
    private DialogueService dialogueService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MqttServerPublish mqttServerPublish;

    @Resource
    private TtsServiceFactory ttsServiceFactory;

    @Resource
    private OpusProcessor opusProcessor;

    @Resource
    private MediaMapper mediaMapper;

    private final String TaskDelayQueue = "xiaozhi:task:queue";

    // 使用虚拟线程池处理定时任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(
            Runtime.getRuntime().availableProcessors(),
            Thread.ofVirtual().name("audio-scheduler-", 0).factory());

    // 音频发送相关常量
    private static final long OPUS_FRAME_SEND_INTERVAL_MS = AudioUtils.OPUS_FRAME_DURATION_MS;
    private static final int PRE_BUFFER_FRAMES = 3;

    // Lua 脚本：原子地取出并删除 0-now 之间的数据
    private final String ZRangeScript = """
            local msgs = redis.call('ZRANGEBYSCORE', KEYS[1], 0, ARGV[1], 'limit', 0, 100)
            if (#msgs > 0) then
            redis.call('ZREM', KEYS[1], unpack(msgs))
            return msgs
            else return {} end
            """;

    @Scheduled(fixedDelay = 90 * 1000)
    public void test() {
        var media = mediaMapper.findLatest("news");
        if (media == null) return;

        var deviceId = "74:4d:bd:7f:2f:98";
        var topic = STR."devices/p2p/GID@@@\{deviceId.replaceAll(":", "_")}";

        mqttServerPublish.send(topic, "{\"type\":\"player\"}");

        Thread.startVirtualThread(() -> {
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            var newSession = sessionManager.getSessionByDeviceId(deviceId);
            if (newSession == null) return;

            sendAudioMessage(newSession, STR."audio/\{media.getTitle()}.opus", media.getTitle(), true, true);
        });
    }

    //@Scheduled(cron = "0 */1 * * * ?")
    public void run() {
        var maxRange = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        var taskJsonArray = Optional.ofNullable(stringRedisTemplate.opsForZSet().rangeByScore(TaskDelayQueue, 0, maxRange))
                .orElseGet(Set::of);

        log.info("From {} to {} with zone {} get tasks {}", 0, maxRange, ZoneId.systemDefault(), taskJsonArray);

        var tasks = taskJsonArray.stream()
                .map(Integer::valueOf)
                .toList();

        for (var taskId : tasks) {
            var instance = taskInstanceMapper.selectById(taskId);
            if (instance == null) continue;
            if (instance.getStatus() != TaskStatus.Waiting) continue;

            var now = Instant.now();

            // create task chain
            var durations = new ArrayList<Integer>();
            for (int i = 0; i < instance.getDuration().length(); i += 2) {
                durations.add(Integer.parseInt(instance.getDuration().substring(i, i + 2)));
            }

            var idx = new AtomicInteger(0);
            var dummy = new TaskChain();
            Arrays.stream(TaskContent.values())
                    .filter(it -> (it.getValue() & instance.getContent()) != 0)
                    .map(it -> new TaskChain().setInstanceId(instance.getId()).setContent(it).setStartTime(now).setDuration(durations.get(idx.getAndIncrement())))
                    .reduce(dummy, (z, x) -> {
                        z.setNext(x);
                        return x;
                    }, (_, it) -> it);

            var taskChain = dummy.getNext();
            var topic = STR."devices/p2p/GID@@@\{instance.getDeviceId().replaceAll(":", "_")}";

            var session = sessionManager.getSessionByDeviceId(instance.getDeviceId());
            if (session != null) continue;

            switch (instance.getType()) {
                // case Listening, Information -> {
                //     mqttServerPublish.open(topic, "player", "news");
                //     sessionManager.putListeningStopTime(instance.getDeviceId(), Instant.now().plusSeconds(taskChain.getDuration() * 60));
                //     instance.setStatus(TaskStatus.Running);
                //     taskInstanceMapper.updateById(instance);
                //     // remove when task started
                //     stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
                // }

                case Listening -> {
                    var media = mediaMapper.findLatest("story");
                    if (media == null) return;
                    mqttServerPublish.send(topic, "{\"type\":\"player\"}");

                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }

                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;

                        instance.setStatus(TaskStatus.Running);
                        taskInstanceMapper.updateById(instance);
                        // remove when task started
                        stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());

                        sendAudioMessage(newSession, media.getAssetUrl(), media.getTitle(), true, true);
                    });
                }

                case Information -> {
                    var media = mediaMapper.findLatest("news");
                    if (media == null) return;
                    mqttServerPublish.send(topic, "{\"type\":\"player\"}");

                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }

                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;

                        instance.setStatus(TaskStatus.Running);
                        taskInstanceMapper.updateById(instance);
                        // remove when task started
                        stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());

                        sendAudioMessage(newSession, media.getAssetUrl(), media.getTitle(), true, true);
                    });

                }
                case Bedtime, Conversation -> {
                    // wakeup
                    // if (session == null || !session.isOpen()) {
                    // }
                    mqttServerPublish.wakeup(topic);
                    Thread.startVirtualThread(() -> {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                        var newSession = sessionManager.getSessionByDeviceId(instance.getDeviceId());
                        if (newSession == null) return;
                        newSession.setTaskChain(taskChain);

                        if (newSession.isPlaying()) {
                            // 设备正在播放，将任务置为取消
                            instance.setStatus(TaskStatus.Canceled);
                            taskInstanceMapper.updateById(instance);
                            return;
                        }

                        var managerQuery = new OhMyLambdaQueryWrapper<Manager>()
                                .eq(Manager::getId, instance.getManagerId())
                                .select(Manager::getId, Manager::getName, Manager::getCefr);
                        var manager = managerMapper.selectOne(managerQuery);

                        var hello = chatService.getStartSentence(manager.getName(), manager.getCefr(), "【】");
                        dialogueService.sendSentenceWithoutSegmentation(newSession, hello, true)
                                .thenRun(() -> {
                                    instance.setStatus(TaskStatus.Running);
                                    taskInstanceMapper.updateById(instance);
                                    // remove when task started
                                    stringRedisTemplate.opsForZSet().remove(TaskDelayQueue, taskId.toString());
                                });
                    });

                }
            }

        }
    }

    /**
     * 发送音频消息（基于AudioService实现）
     *
     * @param session   ChatSession会话
     * @param audioPath 音频文件路径
     * @param text      文本内容
     * @param isFirst   是否第一句
     * @param isLast    是否最后一句
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendAudioMessage(ChatSession session, String audioPath, String text, boolean isFirst,
                                                    boolean isLast) {
        String sessionId = session.getSessionId();

        // 初始化播放状态
        session.setAudioPlaying(true);
        session.setLastActivityTime(Instant.now());

        return sendMessageHeaders(session, text, isFirst)
                .thenCompose(v -> processAudioContent(session, audioPath))
                .whenComplete((result, error) -> finalizePlayback(session, sessionId))
                .thenCompose(v -> sendStopIfNeeded(session, isLast))
                .exceptionally(error -> handleSendError(session, isLast, error));
    }

    /**
     * 发送消息头部信息（TTS开始、句子开始）
     */
    private CompletableFuture<Void> sendMessageHeaders(ChatSession session, String text, boolean isFirst) {
        CompletableFuture<Void> startFuture = isFirst
                ? CompletableFuture.runAsync(session::sendTTSStart)
                : CompletableFuture.completedFuture(null);

        return startFuture
                .thenRun(() -> session.sendSentenceStart(text));
    }

    /**
     * 处理音频内容
     */
    private CompletableFuture<Void> processAudioContent(ChatSession session, String audioPath) {
        if (audioPath == null) {
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }

        return loadAudioFrames(session.getSessionId(), audioPath)
                .thenCompose(frames -> transmitAudioFrames(session, frames));
    }

    /**
     * 加载音频帧数据
     */
    private CompletableFuture<List<byte[]>> loadAudioFrames(String sessionId, String audioPath) {
        return CompletableFuture.supplyAsync(() -> {
            File audioFile = new File(audioPath);
            if (!audioFile.exists()) {
                log.warn("音频文件不存在: {}", audioPath);
                return null;
            }

            try {
                if (audioPath.contains(".opus")) {
                    return opusProcessor.readOpus(audioFile);
                } else {
                    byte[] audioData = AudioUtils.readAsPcm(audioPath);
                    return opusProcessor.pcmToOpus(sessionId, audioData, false);
                }
            } catch (Exception e) {
                log.error("处理音频文件失败: {}", audioPath, e);
                return null;
            }
        });
    }

    /**
     * 传输音频帧数据
     */
    private CompletableFuture<Void> transmitAudioFrames(ChatSession session, List<byte[]> opusFrames) {
        if (opusFrames == null || opusFrames.isEmpty()) {
            session.setAudioPlaying(false);
            return CompletableFuture.completedFuture(null);
        }

        session.setAudioPlaying(true);

        CompletableFuture<Void> transmissionFuture = new CompletableFuture<>();

        long startTime = System.nanoTime();
        try {
            sendPreBufferedFrames(session, opusFrames);
            scheduleRemainingFrames(session, opusFrames, startTime, transmissionFuture);
        } catch (Exception e) {
            log.error("音频帧传输初始化失败", e);
            transmissionFuture.completeExceptionally(e);
        }

        return transmissionFuture;
    }

    /**
     * 发送预缓冲帧
     */
    private void sendPreBufferedFrames(ChatSession session, List<byte[]> opusFrames) throws IOException {
        int preBufferCount = Math.min(PRE_BUFFER_FRAMES, opusFrames.size());
        for (int i = 0; i < preBufferCount; i++) {
            sendOpusFrame(session, opusFrames.get(i));
        }
    }

    /**
     * 调度剩余帧的发送
     */
    private void scheduleRemainingFrames(ChatSession session, List<byte[]> opusFrames, long startTime,
                                         CompletableFuture<Void> future) {
        int startFrameIndex = Math.min(PRE_BUFFER_FRAMES, opusFrames.size());

        if (startFrameIndex >= opusFrames.size()) {
            future.complete(null);
            return;
        }

        scheduleFrameTransmission(session, opusFrames, startFrameIndex, startTime, future);
    }

    /**
     * 调度单个帧的传输
     */
    private void scheduleFrameTransmission(ChatSession session, List<byte[]> opusFrames,
                                           int frameIndex, long startTime, CompletableFuture<Void> future) {

        session.setLastActivityTime(Instant.now());
        Runnable frameTask = () -> {
            try {
                if (!session.isAudioPlaying() || frameIndex >= opusFrames.size() || !session.isOpen()) {
                    future.complete(null);
                    return;
                }

                sendOpusFrame(session, opusFrames.get(frameIndex));

                var nextFrameIndex = frameIndex + 1;
                if (nextFrameIndex < opusFrames.size()) {
                    scheduleFrameTransmission(session, opusFrames, nextFrameIndex, startTime, future);
                } else {
                    future.complete(null);
                }
            } catch (Exception e) {
                log.error("帧发送失败", e);
                future.completeExceptionally(e);
            }
        };

        scheduleNextFrame(frameIndex, startTime, frameTask);
    }

    /**
     * 发送Opus帧数据
     */
    private void sendOpusFrame(ChatSession session, byte[] opusFrame) throws IOException {
        session.sendBinaryMessage(opusFrame);
    }

    /**
     * 完成播放处理
     */
    private void finalizePlayback(ChatSession session, String sessionId) {
        session.setAudioPlaying(false);
    }

    /**
     * 根据需要发送停止信号
     */
    private CompletableFuture<Void> sendStopIfNeeded(ChatSession session, boolean isLast) {
        if (isLast) {
            return CompletableFuture.runAsync(session::sendTTSStop);
        }
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理发送错误
     */
    private Void handleSendError(ChatSession session, boolean isLast, Throwable error) {
        log.error("音频发送失败", error);
        session.setAudioPlaying(false);
        if (isLast) {
            session.sendTTSStop();
        }
        return null;
    }

    /**
     * 计算并调度下一帧的发送时间
     */
    private void scheduleNextFrame(int frameIndex, long startTime, Runnable frameTask) {
        // 直接用帧索引计算预期发送时间（纳秒级精度）
        long expectedTime = startTime + frameIndex * OPUS_FRAME_SEND_INTERVAL_MS * 1_000_000;
        long currentTime = System.nanoTime();
        long delayNanos = expectedTime - currentTime;

        if (delayNanos <= 0) {
            // 如果当前时间已经超过预期时间，立即发送
            scheduler.schedule(frameTask, 0, TimeUnit.NANOSECONDS);
        } else {
            // 延迟到精确时间点再发送
            scheduler.schedule(frameTask, delayNanos, TimeUnit.NANOSECONDS);
        }
    }

    /**
     * 清理会话资源（公共接口）
     */
    public void cleanupSession(String sessionId) {
        opusProcessor.cleanup(sessionId);
    }

    /**
     * 直接发送音频帧数据
     *
     * @param session    ChatSession会话
     * @param opusFrames Opus音频帧列表
     * @param text       文本内容
     * @param isFirst    是否第一句
     * @param isLast     是否最后一句
     * @return 操作完成的CompletableFuture
     */
    public CompletableFuture<Void> sendAudioFrames(ChatSession session, List<byte[]> opusFrames, String text,
                                                   boolean isFirst, boolean isLast) {
        String sessionId = session.getSessionId();

        // 初始化播放状态
        session.setAudioPlaying(true);
        session.setLastActivityTime(Instant.now());

        return sendMessageHeaders(session, text, isFirst)
                .thenCompose(v -> transmitAudioFrames(session, opusFrames))
                .whenComplete((result, error) -> finalizePlayback(session, sessionId))
                .thenCompose(v -> sendStopIfNeeded(session, isLast))
                .exceptionally(error -> handleSendError(session, isLast, error));
    }

}
