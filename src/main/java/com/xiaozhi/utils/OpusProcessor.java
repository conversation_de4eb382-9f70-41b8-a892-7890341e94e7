package com.xiaozhi.utils;

import io.github.jaredmdobson.concentus.*;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.ShortBuffer;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class OpusProcessor {
    private static final Logger logger = LoggerFactory.getLogger(OpusProcessor.class);

    // 缓存
    private final ConcurrentHashMap<String, OpusDecoder> decoders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, OpusEncoder> encoders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, short[]> overlaps = new ConcurrentHashMap<>();
    // 残留数据状态缓存
    private final ConcurrentHashMap<String, LeftoverState> leftoverStates = new ConcurrentHashMap<>();

    // 常量
    private static final int FRAME_SIZE = AudioUtils.FRAME_SIZE;
    private static final int SAMPLE_RATE = AudioUtils.SAMPLE_RATE;
    private static final int CHANNELS = AudioUtils.CHANNELS;
    public static final int OPUS_FRAME_DURATION_MS = AudioUtils.OPUS_FRAME_DURATION_MS;
    private static final int MAX_SIZE = 1275;

    // 预热帧数量 - 添加几个静音帧来预热编解码器
    private static final int PRE_WARM_FRAMES = 2;

    /**
     * 残留数据状态类
     */
    public static class LeftoverState {
        public short[] leftoverBuffer;
        public int leftoverCount;
        public boolean isFirst = true;

        public LeftoverState() {
            leftoverBuffer = new short[FRAME_SIZE]; // 预分配一个帧大小的缓冲区
            leftoverCount = 0;
        }

        public void clear() {
            leftoverCount = 0;
            Arrays.fill(leftoverBuffer, (short) 0);
        }
    }

    /**
     * 获取会话的残留数据状态
     */
    private LeftoverState getLeftoverState(String sid) {
        return leftoverStates.computeIfAbsent(sid, k -> new LeftoverState());
    }

    /**
     * 删除会话的残留数据状态
     */
    public void removeLeftoverState(String sid) {
        leftoverStates.remove(sid);
    }

    /**
     * 刷新残留数据，生成最后一帧
     */
    public List<byte[]> flushLeftover(String sid) {
        LeftoverState state = getLeftoverState(sid);
        List<byte[]> frames = new ArrayList<>();

        if (state.leftoverCount <= 0) {
            return frames;
        }

        // 获取编码器
        OpusEncoder encoder = getEncoder(sid, SAMPLE_RATE, CHANNELS);

        // 准备缓冲区
        short[] shortBuf = new short[FRAME_SIZE];
        byte[] opusBuf = new byte[MAX_SIZE];

        // 复制残留数据并填充静音
        System.arraycopy(state.leftoverBuffer, 0, shortBuf, 0, state.leftoverCount);
        Arrays.fill(shortBuf, state.leftoverCount, FRAME_SIZE, (short) 0);

        try {
            // 编码最后一帧
            int opusLen = encoder.encode(shortBuf, 0, FRAME_SIZE, opusBuf, 0, opusBuf.length);
            if (opusLen > 0) {
                byte[] frame = new byte[opusLen];
                System.arraycopy(opusBuf, 0, frame, 0, opusLen);
                frames.add(frame);
            }
        } catch (OpusException e) {
            logger.warn("残留数据编码失败: {}", e.getMessage());
        }

        // 清空缓存
        state.clear();
        return frames;
    }

    /**
     * Opus转PCM字节数组
     */
    public byte[] opusToPcm(String sid, byte[] data) throws OpusException {
        if (data == null || data.length == 0) {
            return new byte[0];
        }

        try {
            OpusDecoder decoder = getDecoder(sid);
            short[] buf = new short[FRAME_SIZE * 12];
            int samples = decoder.decode(data, 0, data.length, buf, 0, buf.length, false);

            if (samples <= 0) {
                logger.debug("解码器返回0个样本，可能是静音帧");
                return new byte[0];
            }

            byte[] pcm = new byte[samples * 2];
            for (int i = 0; i < samples; i++) {
                pcm[i * 2] = (byte) (buf[i] & 0xFF);
                pcm[i * 2 + 1] = (byte) ((buf[i] >> 8) & 0xFF);
            }

            return pcm;
        } catch (OpusException e) {
            logger.warn("Opus解码失败: {} (数据长度: {})", e.getMessage(), data.length);
            // 不要立即重置解码器，可能只是单个帧的问题
            throw e;
        }
    }

    /**
     * 平滑连接多个PCM片段
     */
    public byte[] smoothJoinPcm(List<byte[]> pcmChunks) {
        if (pcmChunks == null || pcmChunks.isEmpty()) {
            return new byte[0];
        }

        // 计算总长度
        int totalLength = 0;
        for (byte[] chunk : pcmChunks) {
            totalLength += chunk.length;
        }

        // 创建结果缓冲区
        byte[] result = new byte[totalLength];
        int offset = 0;

        // 应用交叉淡入淡出的重叠区域长度（毫秒）
        int overlapMs = 10; // 10ms的重叠
        int overlapBytes = (SAMPLE_RATE * 2 * CHANNELS * overlapMs) / 1000; // 每毫秒的字节数

        for (int i = 0; i < pcmChunks.size(); i++) {
            byte[] chunk = pcmChunks.get(i);

            if (i == 0) {
                // 第一个片段直接复制
                System.arraycopy(chunk, 0, result, offset, chunk.length);
                offset += chunk.length;
            } else {
                // 后续片段需要与前一个片段进行平滑过渡
                int overlapStart = Math.max(0, offset - overlapBytes);
                int overlapLength = Math.min(overlapBytes, offset - overlapStart);

                if (overlapLength > 0 && chunk.length > 0) {
                    // 在重叠区域应用线性交叉淡变
                    for (int j = 0; j < overlapLength; j += 2) {
                        // 计算淡变权重
                        float weight = (float) j / overlapLength;

                        // 获取重叠区域的样本
                        short sample1 = (short) ((result[overlapStart + j] & 0xFF) | ((result[overlapStart + j + 1] & 0xFF) << 8));
                        short sample2 = (short) ((chunk[j] & 0xFF) | ((chunk[j + 1] & 0xFF) << 8));

                        // 线性混合
                        short mixed = (short) ((1 - weight) * sample1 + weight * sample2);

                        // 写回结果
                        result[overlapStart + j] = (byte) (mixed & 0xFF);
                        result[overlapStart + j + 1] = (byte) ((mixed >> 8) & 0xFF);
                    }

                    // 复制剩余部分
                    System.arraycopy(chunk, overlapLength, result, offset, chunk.length - overlapLength);
                    offset += (chunk.length - overlapLength);
                } else {
                    // 没有足够的重叠，直接复制
                    System.arraycopy(chunk, 0, result, offset, chunk.length);
                    offset += chunk.length;
                }
            }
        }

        // 如果实际长度小于预分配长度，则裁剪
        if (offset < totalLength) {
            byte[] trimmed = new byte[offset];
            System.arraycopy(result, 0, trimmed, 0, offset);
            return trimmed;
        }

        return result;
    }

    /**
     * Opus转short数组
     */
    public short[] opusToShort(String sid, byte[] data) throws OpusException {
        if (data == null || data.length == 0) {
            return new short[0];
        }

        try {
            OpusDecoder decoder = getDecoder(sid);
            short[] buf = new short[FRAME_SIZE * 6];
            int samples = decoder.decode(data, 0, data.length, buf, 0, buf.length, false);

            if (samples < buf.length) {
                short[] result = new short[samples];
                System.arraycopy(buf, 0, result, 0, samples);
                return result;
            }

            return buf;
        } catch (OpusException e) {
            logger.warn("解码失败: {}", e.getMessage());
            resetDecoder(sid);
            throw e;
        }
    }

    /**
     * OGG转PCM
     */
    public byte[] oggToPcm(String sid, byte[] data) {
        if (data == null || data.length == 0) {
            return new byte[0];
        }

        try {
            // 检查OGG格式
            if (data.length < 4 || data[0] != 'O' || data[1] != 'g' || data[2] != 'g' || data[3] != 'S') {
                try {
                    // 尝试直接解码
                    return opusToPcm(sid, data);
                } catch (OpusException e) {
                    logger.warn("非OGG格式解码失败: {}", e.getMessage());
                    return new byte[0];
                }
            }

            // 解析OGG
            List<byte[]> packets = parseOgg(data);

            if (packets.isEmpty()) {
                logger.warn("OGG中无数据包");
                return new byte[0];
            }

            // 重置解码器以确保干净的状态
            resetDecoder(sid);

            // 解码所有包
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int count = 0;
            boolean foundOpusHead = false;

            for (byte[] packet : packets) {
                try {
                    if (packet.length == 0) continue;

                    // 检查是否是 OpusHead 包
                    if (packet.length >= 8 &&
                        packet[0] == 'O' && packet[1] == 'p' && packet[2] == 'u' && packet[3] == 's' &&
                        packet[4] == 'H' && packet[5] == 'e' && packet[6] == 'a' && packet[7] == 'd') {
                        foundOpusHead = true;
                        logger.debug("跳过 OpusHead 包");
                        continue;
                    }

                    // 检查是否是 OpusTags 包
                    if (packet.length >= 8 &&
                        packet[0] == 'O' && packet[1] == 'p' && packet[2] == 'u' && packet[3] == 's' &&
                        packet[4] == 'T' && packet[5] == 'a' && packet[6] == 'g' && packet[7] == 's') {
                        logger.debug("跳过 OpusTags 包");
                        continue;
                    }

                    // 只有在找到 OpusHead 后才处理音频数据包
                    if (foundOpusHead && packet.length > 0) {
                        // 验证 Opus 帧的有效性
                        if (isValidOpusFrame(packet)) {
                            byte[] pcm = opusToPcm(sid, packet);
                            if (pcm.length > 0) {
                                out.write(pcm);
                                count++;
                            }
                        } else {
                            logger.debug("跳过无效的 Opus 帧，长度: {}", packet.length);
                        }
                    }
                } catch (OpusException e) {
                    logger.warn("包解码失败，跳过此包: {}", e.getMessage());
                    // 继续处理下一个包，不要因为一个包失败就停止
                }
            }

            logger.info("成功解码 {} 个音频包", count);
            return out.toByteArray();

        } catch (Exception e) {
            logger.error("OGG解码错误", e);
            return new byte[0];
        }
    }

    /**
     * 解析OGG格式
     */
    private List<byte[]> parseOgg(byte[] data) {
        List<byte[]> packets = new ArrayList<>();
        int offset = 0;
        int pageCount = 0;

        logger.debug("开始解析 OGG 数据，总长度: {} 字节", data.length);

        while (offset < data.length - 27) { // OGG 页面头最少 27 字节
            try {
                // 查找 OGG 页面标识
                if (data[offset] != 'O' || data[offset + 1] != 'g' ||
                    data[offset + 2] != 'g' || data[offset + 3] != 'S') {
                    // 搜索下一个 OGG 页面
                    boolean found = false;
                    for (int i = offset + 1; i < data.length - 3; i++) {
                        if (data[i] == 'O' && data[i + 1] == 'g' &&
                            data[i + 2] == 'g' && data[i + 3] == 'S') {
                            offset = i;
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        logger.debug("未找到更多 OGG 页面");
                        break;
                    }
                }

                pageCount++;
                logger.debug("处理 OGG 页面 #{} 在偏移 {}", pageCount, offset);

                // 跳过 OGG 页面头：OggS(4) + version(1) + flags(1) + granule(8) + serial(4) + sequence(4) + checksum(4) = 26 字节
                int headerOffset = offset + 26;

                if (headerOffset >= data.length) {
                    logger.debug("页面头超出数据范围");
                    break;
                }

                // 读取分段数
                int segCount = data[headerOffset] & 0xFF;
                headerOffset++;

                if (headerOffset + segCount > data.length) {
                    logger.debug("分段表超出数据范围");
                    break;
                }

                logger.debug("页面 #{} 有 {} 个分段", pageCount, segCount);

                // 读取分段表
                byte[] segments = new byte[segCount];
                System.arraycopy(data, headerOffset, segments, 0, segCount);
                headerOffset += segCount;

                // 计算数据长度
                int dataLen = 0;
                for (int i = 0; i < segCount; i++) {
                    dataLen += segments[i] & 0xFF;
                }
                logger.debug("页面 #{} 数据长度: {} 字节", pageCount, dataLen);

                if (headerOffset + dataLen > data.length) {
                    logger.debug("页面数据超出文件范围");
                    break;
                }

                // 提取页面数据
                byte[] pageData = new byte[dataLen];
                System.arraycopy(data, headerOffset, pageData, 0, dataLen);

                // 解析数据包
                int dataOffset = 0;
                int packetLen = 0;
                int packetsInPage = 0;

                for (int i = 0; i < segCount; i++) {
                    int segLen = segments[i] & 0xFF;
                    packetLen += segLen;

                    // 如果段长不是255，表示包结束
                    if (segLen != 255) {
                        if (packetLen > 0 && dataOffset + packetLen <= pageData.length) {
                            byte[] packet = new byte[packetLen];
                            System.arraycopy(pageData, dataOffset, packet, 0, packetLen);
                            packets.add(packet);
                            packetsInPage++;

                            // 调试：打印数据包内容
                            if (packetsInPage <= 3) {
                                StringBuilder hex = new StringBuilder();
                                StringBuilder ascii = new StringBuilder();
                                for (int k = 0; k < Math.min(16, packetLen); k++) {
                                    hex.append(String.format("%02X ", packet[k] & 0xFF));
                                    char c = (char) (packet[k] & 0xFF);
                                    ascii.append(Character.isISOControl(c) ? '.' : c);
                                }
                                logger.debug("页面 #{} 数据包 #{} (长度: {}): {} | {}",
                                    pageCount, packetsInPage, packetLen, hex.toString(), ascii.toString());
                            }
                        }
                        dataOffset += packetLen;
                        packetLen = 0;
                    }
                }

                // 处理最后一个包
                if (packetLen > 0 && dataOffset + packetLen <= pageData.length) {
                    byte[] packet = new byte[packetLen];
                    System.arraycopy(pageData, dataOffset, packet, 0, packetLen);
                    packets.add(packet);
                    packetsInPage++;
                    logger.debug("页面 #{} 添加最后数据包 #{} (长度: {})", pageCount, packetsInPage, packetLen);
                }

                logger.debug("页面 #{} 解析完成，共 {} 个数据包", pageCount, packetsInPage);

                // 移动到下一个页面
                offset = headerOffset + dataLen;

            } catch (Exception e) {
                logger.warn("OGG页面 #{} 解析错误: {}", pageCount, e.getMessage());
                break;
            }
        }

        logger.debug("OGG 解析完成，共 {} 个页面，{} 个数据包", pageCount, packets.size());
        return packets;
    }

    /**
     * 读取Opus文件
     */
    public List<byte[]> readOpus(File file) throws IOException {
        List<byte[]> frames = new ArrayList<>();

        // 检查文件大小
        long size = file.length();
        if (size <= 0) {
            logger.warn("空文件: {}", file.getPath());
            return frames;
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            // 检查文件格式
            byte[] header = new byte[8];
            fis.read(header, 0, Math.min(8, (int) size));
            fis.getChannel().position(0);

            // 检查OGG格式
            if (isOgg(header)) {
                return readOgg(file);
            }

            // 检查原始Opus格式
            if (isOpusHead(header)) {
                return readRaw(fis);
            }

            // 尝试帧格式
            frames = readFramed(fis);
            if (!frames.isEmpty()) {
                return frames;
            }

            return readWhole(file);
        } catch (Exception e) {
            logger.error("读取失败: {}", file.getName(), e);
            return readWhole(file);
        }
    }

    /**
     * 读取Opus文件
     */
    public List<byte[]> readOpus(FileInputStream inputStream) throws IOException {
        // 检查文件格式
        byte[] header = new byte[8];
        inputStream.read(header, 0, 8);
        inputStream.getChannel().position(0);

        // 检查OGG格式
        if (isOgg(header)) {
            logger.info("OGG file");
        }

        // 检查原始Opus格式
        if (isOpusHead(header)) {
            return readRaw(inputStream);
        }

        // 尝试帧格式
        var frames = readFramed(inputStream);
        if (!frames.isEmpty()) {
            return frames;
        }

        return List.of();
    }

    /**
     * 检查OGG格式
     */
    private boolean isOgg(byte[] data) {
        return data.length >= 4 &&
                data[0] == 'O' && data[1] == 'g' &&
                data[2] == 'g' && data[3] == 'S';
    }

    /**
     * 检查Opus头
     */
    private boolean isOpusHead(byte[] data) {
        return data.length >= 8 &&
                data[0] == 'O' && data[1] == 'p' &&
                data[2] == 'u' && data[3] == 's' &&
                data[4] == 'H' && data[5] == 'e' &&
                data[6] == 'a' && data[7] == 'd';
    }

    /**
     * 验证 Opus 帧是否有效
     */
    private boolean isValidOpusFrame(byte[] frame) {
        if (frame == null || frame.length == 0) {
            return false;
        }

        // Opus 帧长度通常在 2-1275 字节之间
        if (frame.length < 2 || frame.length > 1275) {
            return false;
        }

        // 检查 TOC (Table of Contents) 字节
        int toc = frame[0] & 0xFF;

        // 检查配置号 (0-31)
        int config = (toc >> 3) & 0x1F;
        if (config > 31) {
            return false;
        }

        return true;
    }

    /**
     * 读取OGG文件
     */
    private List<byte[]> readOgg(File file) throws IOException {
        // 使用Files.readAllBytes代替FileInputStream.readAllBytes
        byte[] data = Files.readAllBytes(file.toPath());
        logger.info("读取 OGG 文件: {} 字节", data.length);

        List<byte[]> packets = parseOgg(data);
        logger.info("从 OGG 解析出 {} 个数据包", packets.size());

        // 过滤并处理 Opus 数据包
        List<byte[]> opusFrames = new ArrayList<>();
        boolean foundOpusHead = false;
        int packetIndex = 0;

        for (byte[] packet : packets) {
            packetIndex++;
            if (packet.length == 0) {
                logger.debug("跳过空数据包 #{}", packetIndex);
                continue;
            }

            // 只在调试模式下打印前几个数据包的详细信息
            if (packetIndex <= 3 && logger.isDebugEnabled()) {
                StringBuilder hexStr = new StringBuilder();
                StringBuilder asciiStr = new StringBuilder();
                for (int i = 0; i < Math.min(16, packet.length); i++) {
                    hexStr.append(String.format("%02X ", packet[i] & 0xFF));
                    char c = (char) (packet[i] & 0xFF);
                    asciiStr.append(Character.isISOControl(c) ? '.' : c);
                }
                logger.debug("数据包 #{} (长度: {}): {} | {}", packetIndex, packet.length, hexStr.toString(), asciiStr.toString());
            }

            // 检查是否是 OpusHead 包
            if (packet.length >= 8 &&
                packet[0] == 'O' && packet[1] == 'p' && packet[2] == 'u' && packet[3] == 's' &&
                packet[4] == 'H' && packet[5] == 'e' && packet[6] == 'a' && packet[7] == 'd') {
                foundOpusHead = true;
                logger.debug("找到 OpusHead 包，长度: {}", packet.length);
                continue;
            }

            // 检查是否是 OpusTags 包
            if (packet.length >= 8 &&
                packet[0] == 'O' && packet[1] == 'p' && packet[2] == 'u' && packet[3] == 's' &&
                packet[4] == 'T' && packet[5] == 'a' && packet[6] == 'g' && packet[7] == 's') {
                logger.debug("跳过 OpusTags 包，长度: {}", packet.length);
                continue;
            }

            // 只有在找到 OpusHead 后才处理音频数据包
            if (foundOpusHead && packet.length > 0) {
                // 验证 Opus 帧的有效性
                if (isValidOpusFrame(packet)) {
                    opusFrames.add(packet);
                    logger.debug("添加有效 Opus 帧 #{}, 长度: {}", opusFrames.size(), packet.length);
                } else {
                    logger.debug("跳过无效的 Opus 帧，长度: {}", packet.length);
                }
            } else if (!foundOpusHead) {
                logger.debug("尚未找到 OpusHead，跳过数据包 #{}", packetIndex);
            }
        }

        logger.info("从 OGG 文件解析出 {} 个 Opus 音频帧", opusFrames.size());
        return opusFrames;
    }

    /**
     * 读取原始Opus文件
     */
    private List<byte[]> readRaw(FileInputStream fis) throws IOException {
        List<byte[]> frames = new ArrayList<>();

        // 跳过头部
        fis.skip(19);

        byte[] buffer = new byte[4096];
        int read;

        while ((read = fis.read(buffer)) > 0) {
            byte[] frame = new byte[read];
            System.arraycopy(buffer, 0, frame, 0, read);
            frames.add(frame);
        }

        return frames;
    }

    /**
     * 读取帧格式文件
     */
    private List<byte[]> readFramed(FileInputStream fis) throws IOException {
        // 尝试2字节帧头
        fis.getChannel().position(0);
        List<byte[]> frames = readFrames(fis, 2);

        if (!frames.isEmpty()) {
            logger.info("2字节帧头成功: {} 帧", frames.size());
            return frames;
        }

        // 尝试4字节帧头
        fis.getChannel().position(0);
        frames = readFrames(fis, 4);

        if (!frames.isEmpty()) {
            logger.info("4字节帧头成功: {} 帧", frames.size());
            return frames;
        }

        // 尝试固定大小帧
        fis.getChannel().position(0);
        frames = readFixed(fis, 80);

        if (!frames.isEmpty()) {
            logger.info("固定帧成功: {} 帧", frames.size());
        }

        return frames;
    }

    /**
     * 读取带帧头的文件
     */
    private List<byte[]> readFrames(FileInputStream fis, int headerSize) throws IOException {
        List<byte[]> frames = new ArrayList<>();
        byte[] buffer = new byte[MAX_SIZE];
        byte[] sizeBytes = new byte[headerSize];
        int good = 0;
        int bad = 0;

        while (fis.read(sizeBytes, 0, headerSize) == headerSize) {
            int frameSize = getFrameSize(sizeBytes, headerSize);

            // 检查帧大小
            if (frameSize <= 0 || frameSize > MAX_SIZE) {
                bad++;
                if (bad > 3)
                    break;
                continue;
            }

            // 读取帧
            int read = fis.read(buffer, 0, frameSize);
            if (read != frameSize)
                break;

            byte[] frame = new byte[frameSize];
            System.arraycopy(buffer, 0, frame, 0, frameSize);
            frames.add(frame);
            good++;

            // 5个有效帧就认为格式正确
            if (good >= 5)
                return frames;
        }

        // 帧太少，可能不是正确格式
        if (good < 5)
            frames.clear();

        return frames;
    }

    /**
     * 获取帧大小
     */
    private int getFrameSize(byte[] bytes, int size) {
        if (size == 2) {
            return ((bytes[1] & 0xFF) << 8) | (bytes[0] & 0xFF);
        } else if (size == 4) {
            return ((bytes[3] & 0xFF) << 24) |
                    ((bytes[2] & 0xFF) << 16) |
                    ((bytes[1] & 0xFF) << 8) |
                    (bytes[0] & 0xFF);
        }
        return -1;
    }

    /**
     * 读取固定大小帧
     */
    private List<byte[]> readFixed(FileInputStream fis, int frameSize) throws IOException {
        List<byte[]> frames = new ArrayList<>();
        byte[] buffer = new byte[frameSize];

        int read;
        while ((read = fis.read(buffer)) == frameSize) {
            byte[] frame = new byte[frameSize];
            System.arraycopy(buffer, 0, frame, 0, frameSize);
            frames.add(frame);

            // 足够多的帧
            if (frames.size() >= 5)
                return frames;
        }

        // 处理最后一帧
        if (read > 0) {
            byte[] last = new byte[read];
            System.arraycopy(buffer, 0, last, 0, read);
            frames.add(last);
        }

        return frames;
    }

    /**
     * 读取整个文件作为单帧
     */
    private List<byte[]> readWhole(File file) throws IOException {
        List<byte[]> frames = new ArrayList<>();

        // 使用Files.readAllBytes代替FileInputStream.readAllBytes
        byte[] data = Files.readAllBytes(file.toPath());
        if (data.length > 0) {
            frames.add(data);
            logger.info("整个文件作为单帧: {} 字节", data.length);
        }

        return frames;
    }

    /**
     * 获取解码器
     */
    public OpusDecoder getDecoder(String sid) {
        return decoders.computeIfAbsent(sid, k -> {
            try {
                OpusDecoder decoder = new OpusDecoder(SAMPLE_RATE, CHANNELS);
                // 设置适当的增益，避免过度放大导致失真
                decoder.setGain(0); // 使用默认增益，避免音频失真
                return decoder;
            } catch (OpusException e) {
                logger.error("创建解码器失败", e);
                throw new RuntimeException("创建解码器失败", e);
            }
        });
    }

    /**
     * 重置解码器
     */
    public void resetDecoder(String sid) {
        decoders.remove(sid);
        try {
            getDecoder(sid);
        } catch (Exception e) {
            logger.error("重置解码器失败", e);
        }
    }

    /**
     * PCM转Opus - 改进版，支持残留数据处理
     */
    public List<byte[]> pcmToOpus(String sid, byte[] pcm, boolean isStream) {
        if (pcm == null || pcm.length == 0) {
            return new ArrayList<>();
        }

        // 确保PCM长度是偶数
        int pcmLen = pcm.length;
        if (pcmLen % 2 != 0) {
            pcmLen--;
        }

        // 每帧样本数
        int frameSize = FRAME_SIZE;

        // 获取编码器
        OpusEncoder encoder = getEncoder(sid, SAMPLE_RATE, CHANNELS);

        // 处理PCM
        List<byte[]> frames = new ArrayList<>();

        // 获取残留数据状态
        LeftoverState state = getLeftoverState(sid);

        // 字节序处理
        ByteBuffer pcmBuf = ByteBuffer.wrap(pcm, 0, pcmLen).order(ByteOrder.LITTLE_ENDIAN);
        ShortBuffer inputShorts = pcmBuf.asShortBuffer();
        int totalInputSamples = inputShorts.remaining();

        // 合并残留数据与当前输入
        short[] combined;
        // 缓冲区
        short[] shortBuf = new short[frameSize];
        byte[] opusBuf = new byte[MAX_SIZE];

        if (isStream) {
            if (state.leftoverCount > 0 || !state.isFirst) {
                combined = new short[state.leftoverCount + totalInputSamples];
                System.arraycopy(state.leftoverBuffer, 0, combined, 0, state.leftoverCount);
                inputShorts.get(combined, state.leftoverCount, totalInputSamples);
            } else {
                combined = new short[totalInputSamples];
                inputShorts.get(combined);
                // 如果是流式第一次处理数据，添加预热帧
                if (state.isFirst) {
                    addPreWarmFrames(frames, encoder, frameSize, opusBuf);
                    state.isFirst = false;
                }
            }
        } else {
            combined = new short[totalInputSamples];
            inputShorts.get(combined);
            addPreWarmFrames(frames, encoder, frameSize, opusBuf);
        }

        int availableSamples = combined.length;
        int frameCount = availableSamples / frameSize;
        int remainingSamples = availableSamples % frameSize;

        // 处理第一帧 - 如果是新的音频段，应用淡入效果
        if (frameCount > 0 && state.isFirst) {
            System.arraycopy(combined, 0, shortBuf, 0, frameSize);

            // 应用淡入效果 - 前20毫秒（大约320个样本）
            int fadeInSamples = Math.min(320, frameSize);
            for (int i = 0; i < fadeInSamples; i++) {
                // 线性淡入
                float gain = (float) i / fadeInSamples;
                shortBuf[i] = (short) (shortBuf[i] * gain);
            }

            try {
                int opusLen = encoder.encode(shortBuf, 0, frameSize, opusBuf, 0, opusBuf.length);
                if (opusLen > 0) {
                    frames.add(Arrays.copyOf(opusBuf, opusLen));
                }
            } catch (OpusException e) {
                logger.warn("淡入帧编码失败: {}", e.getMessage());
            }

            // 处理剩余的完整帧
            for (int i = 1; i < frameCount; i++) {
                int start = i * frameSize;
                System.arraycopy(combined, start, shortBuf, 0, frameSize);
                try {
                    int opusLen = encoder.encode(shortBuf, 0, frameSize, opusBuf, 0, opusBuf.length);
                    if (opusLen > 0) {
                        frames.add(Arrays.copyOf(opusBuf, opusLen));
                    }
                } catch (OpusException e) {
                    logger.warn("帧 #{} 编码失败: {}", i, e.getMessage());
                }
            }
        } else {
            // 处理所有完整帧
            for (int i = 0; i < frameCount; i++) {
                int start = i * frameSize;
                System.arraycopy(combined, start, shortBuf, 0, frameSize);
                try {
                    int opusLen = encoder.encode(shortBuf, 0, frameSize, opusBuf, 0, opusBuf.length);
                    if (opusLen > 0) {
                        frames.add(Arrays.copyOf(opusBuf, opusLen));
                    }
                } catch (Exception e) {
                    logger.warn("帧 #{} 编码失败: {}", i, e.getMessage());
                }
            }
        }

        if (isStream) {
            // 缓存剩余样本
            state.leftoverCount = remainingSamples;
            if (remainingSamples > 0) {
                if (state.leftoverBuffer.length < remainingSamples) {
                    state.leftoverBuffer = new short[frameSize]; // 确保缓冲区足够大
                }
                System.arraycopy(combined, frameCount * frameSize, state.leftoverBuffer, 0, remainingSamples);
            } else {
                Arrays.fill(state.leftoverBuffer, (short) 0); // 清空
            }
        }
        return frames;
    }

    /**
     * 添加预热帧 - 解决开头破音问题
     */
    private void addPreWarmFrames(List<byte[]> frames, OpusEncoder encoder, int frameSize, byte[] opusBuf) {
        // 创建静音帧
        short[] silenceBuf = new short[frameSize];
        Arrays.fill(silenceBuf, (short) 0);

        // 添加几个静音帧来预热编码器
        for (int i = 0; i < PRE_WARM_FRAMES; i++) {
            try {
                int opusLen = encoder.encode(silenceBuf, 0, frameSize, opusBuf, 0, opusBuf.length);
                if (opusLen > 0) {
                    byte[] frame = new byte[frameSize];
                    System.arraycopy(opusBuf, 0, frame, 0, frameSize);
                    frames.add(frame);
                }
            } catch (OpusException e) {
                logger.warn("预热帧 #{} 编码失败: {}", i, e.getMessage());
            }
        }
    }

    /**
     * 清理会话
     */
    public void cleanup(String sid) {
        decoders.remove(sid);
        overlaps.remove(sid);
        leftoverStates.remove(sid); // 清理残留数据状态

        // 清理编码器
        List<String> toRemove = new ArrayList<>();
        for (String key : encoders.keySet()) {
            if (key.startsWith(sid + "_")) {
                toRemove.add(key);
            }
        }

        for (String key : toRemove) {
            encoders.remove(key);
        }
    }

    /**
     * 获取编码器
     */
    private OpusEncoder getEncoder(String sid, int rate, int channels) {
        return encoders.computeIfAbsent(sid, k -> {
            try {
                OpusEncoder encoder = new OpusEncoder(rate, channels, OpusApplication.OPUS_APPLICATION_VOIP);

                // 优化设置
                encoder.setBitrate(AudioUtils.BITRATE);
                // 这里后续看是不是要针对音乐做一个切换
                encoder.setSignalType(OpusSignal.OPUS_SIGNAL_VOICE);
                encoder.setComplexity(5); // 复杂度高音质好，低速度快
                encoder.setPacketLossPercent(0); // 降低丢包补偿，减少处理延迟
                encoder.setForceChannels(channels);
                encoder.setUseVBR(false); // 使用CBR模式确保稳定的比特率
                encoder.setUseDTX(false); // 禁用DTX以确保连续的帧

                return encoder;
            } catch (OpusException e) {
                logger.error("创建编码器失败: 采样率={}, 通道={}", rate, channels, e);
                throw new RuntimeException("创建编码器失败", e);
            }
        });
    }

    /**
     * 释放资源
     */
    @PreDestroy
    public void cleanup() {
        decoders.clear();
        encoders.clear();
        overlaps.clear();
        leftoverStates.clear(); // 清理所有残留数据状态
    }
}