package com.xiaozhi.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.List;

public class DynamicFrameDurationTest {
    private static final Logger logger = LoggerFactory.getLogger(DynamicFrameDurationTest.class);
    
    public static void main(String[] args) {
        OpusProcessor processor = new OpusProcessor();
        
        // 测试指定的 OGG 文件
        String filePath = "audio/2025年09月14日资讯.opus";
        File file = new File(filePath);
        
        if (!file.exists()) {
            logger.error("文件不存在: {}", filePath);
            return;
        }
        
        logger.info("测试动态帧持续时间检测: {}", filePath);
        
        try {
            List<byte[]> frames = processor.readOpus(file);
            if (frames == null || frames.isEmpty()) {
                logger.error("无法读取音频帧");
                return;
            }
            
            logger.info("总帧数: {}", frames.size());
            
            // 模拟动态帧持续时间检测
            long detectedDuration = detectFrameDuration("test", frames, processor);
            logger.info("检测到的帧持续时间: {}ms", detectedDuration);
            
            // 计算预期播放时长
            double expectedDurationSeconds = frames.size() * detectedDuration / 1000.0;
            logger.info("预期播放时长: {}秒", String.format("%.1f", expectedDurationSeconds));
            
        } catch (Exception e) {
            logger.error("测试失败", e);
        } finally {
            processor.cleanup("test");
        }
    }
    
    private static long detectFrameDuration(String sessionId, List<byte[]> opusFrames, OpusProcessor processor) {
        if (opusFrames.isEmpty()) {
            return 20; // 默认值
        }
        
        try {
            // 解码前几个帧来检测持续时间
            int totalSamples = 0;
            int frameCount = 0;
            int maxFramesToCheck = Math.min(5, opusFrames.size());
            
            for (int i = 0; i < maxFramesToCheck; i++) {
                try {
                    byte[] pcm = processor.opusToPcm(sessionId, opusFrames.get(i));
                    int samples = pcm.length / 2; // 16位PCM，每个样本2字节
                    totalSamples += samples;
                    frameCount++;
                    logger.debug("帧 #{}: {} 样本", i + 1, samples);
                } catch (Exception e) {
                    logger.debug("跳过无法解码的帧 #{}", i);
                }
            }
            
            if (frameCount > 0) {
                double avgSamplesPerFrame = (double) totalSamples / frameCount;
                long frameDurationMs = Math.round(avgSamplesPerFrame / 16000 * 1000); // 16kHz采样率
                
                logger.info("平均每帧样本数: {}", String.format("%.0f", avgSamplesPerFrame));
                logger.info("计算出的帧持续时间: {}ms", frameDurationMs);
                
                // 验证帧持续时间是否合理（5ms-120ms之间）
                if (frameDurationMs >= 5 && frameDurationMs <= 120) {
                    return frameDurationMs;
                }
            }
        } catch (Exception e) {
            logger.warn("检测帧持续时间失败，使用默认值", e);
        }
        
        return 20; // 使用默认值
    }
}
