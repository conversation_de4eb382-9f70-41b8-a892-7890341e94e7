package com.xiaozhi.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class OpusProcessorTest {
    private static final Logger logger = LoggerFactory.getLogger(OpusProcessorTest.class);
    
    private OpusProcessor opusProcessor;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        opusProcessor = new OpusProcessor();
    }
    
    @Test
    void testOpusFrameProcessing() {
        String sessionId = "test-session";

        // 测试空帧处理
        try {
            byte[] result = opusProcessor.opusToPcm(sessionId, new byte[0]);
            assertEquals(0, result.length);
        } catch (Exception e) {
            // 空帧应该返回空数组而不是抛出异常
            logger.info("空帧处理: {}", e.getMessage());
        }

        // 清理
        opusProcessor.cleanup(sessionId);
    }
    
    @Test
    void testOggFormatDetection() throws IOException {
        // 创建一个模拟的 OGG 文件头
        File oggFile = tempDir.resolve("test.ogg").toFile();
        try (FileOutputStream fos = new FileOutputStream(oggFile)) {
            // 写入 OGG 页面头
            fos.write("OggS".getBytes()); // OGG 标识
            fos.write(new byte[]{0, 2}); // 版本和标志
            fos.write(new byte[16]); // 粒度位置、序列号、页序号、校验和
            fos.write(1); // 分段数
            fos.write(19); // 分段长度
            
            // 写入 OpusHead 包
            fos.write("OpusHead".getBytes());
            fos.write(new byte[11]); // OpusHead 剩余数据
        }
        
        // 测试读取 OGG 文件
        List<byte[]> frames = opusProcessor.readOpus(oggFile);
        assertNotNull(frames);
        logger.info("从测试 OGG 文件读取到 {} 个帧", frames.size());
    }
    
    @Test
    void testOpusDecoderCreation() {
        // 测试解码器创建
        String sessionId = "test-session";
        assertDoesNotThrow(() -> {
            var decoder = opusProcessor.getDecoder(sessionId);
            assertNotNull(decoder);
        });
        
        // 清理
        opusProcessor.cleanup(sessionId);
    }
    
    @Test
    void testEmptyDataHandling() {
        String sessionId = "test-session";
        
        // 测试空数据处理
        byte[] emptyPcm = opusProcessor.oggToPcm(sessionId, new byte[0]);
        assertEquals(0, emptyPcm.length);
        
        byte[] nullPcm = opusProcessor.oggToPcm(sessionId, null);
        assertEquals(0, nullPcm.length);
        
        // 清理
        opusProcessor.cleanup(sessionId);
    }
    
    @Test
    void testInvalidOggData() {
        String sessionId = "test-session";
        
        // 测试无效的 OGG 数据
        byte[] invalidData = "NotOggData".getBytes();
        byte[] result = opusProcessor.oggToPcm(sessionId, invalidData);
        assertEquals(0, result.length);
        
        // 清理
        opusProcessor.cleanup(sessionId);
    }
}
