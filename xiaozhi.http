:header = <<
Content-Type: application/json
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36
Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjEsImV4cCI6MTc1ODQzODczMX0.TLEiSNj0-jP2EbaYRpQViqC0FC-uulHHd8waguTb3fM
# Authorization: eyJhbGciOiJIUzI1NiJ9.eyJpZCI6MSwidXQiOjMsImV4cCI6MTc1NzczNjQwN30.BAkeySBh2kJp_NEHnKJlTKXqWz2WmH0eAhsnNHEdUw4

#
# :host = https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1
:host = http://localhost:8091/api/v1

#

GET https://rdmytikasapitest.lezhilong.cn/xiaozhi-esp32-server/api/v1/d/medias?category=news|story
Authorization: 90:e5:b1:a9:8c:7c

#
GET :host/m/daily-stats
:header

#
POST :host/m/sms-code
:header

{
  "mobile": "16710245700"
}

#
POST :host/m/login
:header

{
  "mobile": "16710245700",
  "code": "471468"
}

#
POST :host/m/p4t
:header

{
  "device_id": "",
  "action": "open",
  "category": "news"
}

#
GET :host/devices/wakeup?device_id=90:e5:b1:a9:8c:7c
:header

#
PUT :host/ota/version
:header

{
  "version": "1.8.9",
  "url": "https://mytikas-testing.oss-cn-beijing.aliyuncs.com/xiaozhi/xiaozhi.bin"
}

#
POST :host/m/p4t
:header

{
  "device_id": "90:e5:b1:a9:8c:7c",
  "action": "open",
  "category": "news"
}

#
POST :host/medias/gen
:header

{
  "title": "生成今日资讯",
  "category": "news"
}